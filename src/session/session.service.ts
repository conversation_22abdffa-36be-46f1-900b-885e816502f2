import { Inject, Injectable } from '@nestjs/common';
import { RedisClientType } from 'redis';
import { v4 as uuidv4 } from 'uuid';
import { redisConfig, UserType } from '../config/redis.config';
import { User, SessionData } from '../interfaces/user.interface';

@Injectable()
export class SessionService {
  constructor(
    @Inject('REDIS_CLIENT') private readonly redisClient: RedisClientType,
  ) {}

  /**
   * 根据用户类型获取对应的TTL
   */
  private getTtlByUserType(userType: UserType): number {
    return redisConfig.ttl[userType] || redisConfig.defaultTtl;
  }

  async createSession(userData: User): Promise<string> {
    const sessionId = uuidv4();
    const sessionData: SessionData = {
      ...userData,
      sessionCreatedAt: new Date(),
    };

    // 根据用户类型获取对应的TTL
    const ttl = this.getTtlByUserType(userData.userType);

    await this.redisClient.set(
      `session:${sessionId}`,
      JSON.stringify(sessionData),
      {
        EX: ttl,
      },
    );
    return sessionId;
  }

  async getSession(sessionId: string): Promise<SessionData | null> {
    const data = await this.redisClient.get(`session:${sessionId}`);
    if (!data) return null;

    const sessionData: SessionData = JSON.parse(data);

    // 根据用户类型刷新过期时间
    const ttl = this.getTtlByUserType(sessionData.userType);
    await this.redisClient.expire(`session:${sessionId}`, ttl);

    return sessionData;
  }

  async updateSession(sessionId: string, userData: Partial<User>): Promise<boolean> {
    const existingSession = await this.getSession(sessionId);
    if (!existingSession) return false;

    // 合并现有数据和新数据
    const updatedSessionData: SessionData = {
      ...existingSession,
      ...userData,
      // 保持原有的sessionCreatedAt
      sessionCreatedAt: existingSession.sessionCreatedAt,
    };

    // 根据用户类型获取TTL
    const ttl = this.getTtlByUserType(updatedSessionData.userType);

    await this.redisClient.set(
      `session:${sessionId}`,
      JSON.stringify(updatedSessionData),
      {
        EX: ttl,
      },
    );
    return true;
  }

  async deleteSession(sessionId: string): Promise<boolean> {
    const result = await this.redisClient.del(`session:${sessionId}`);
    return result === 1;
  }

  /**
   * 获取session的剩余TTL（秒）
   */
  async getSessionTtl(sessionId: string): Promise<number> {
    const ttl = await this.redisClient.ttl(`session:${sessionId}`);
    return ttl > 0 ? ttl : 0;
  }
}