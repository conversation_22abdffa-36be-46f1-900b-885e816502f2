import { <PERSON>, Get, Post, Body, Req, Res } from '@nestjs/common';
import { AppService } from './app.service';
import { SessionService } from './session/session.service';
import { Request, Response } from 'express';
import { UserType, redisConfig } from './config/redis.config';
import { User } from './interfaces/user.interface';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly sessionService: SessionService,
  ) {}

  /**
   * 获取会话TTL信息
   */
  private getSessionTtlInfo(userType: UserType): { seconds: number; description: string } {
    const ttl = redisConfig.ttl[userType];
    const days = Math.floor(ttl / 86400);
    const hours = Math.floor((ttl % 86400) / 3600);

    let description = '';
    if (days > 0) {
      description = `${days}天`;
      if (hours > 0) description += `${hours}小时`;
    } else {
      description = `${hours}小时`;
    }

    return {
      seconds: ttl,
      description: description
    };
  }

  /**
   * 格式化剩余时间
   */
  private formatRemainingTime(seconds: number): string {
    if (seconds <= 0) return '已过期';

    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    let result = '';
    if (days > 0) result += `${days}天`;
    if (hours > 0) result += `${hours}小时`;
    if (minutes > 0) result += `${minutes}分钟`;
    if (remainingSeconds > 0 && days === 0 && hours === 0) result += `${remainingSeconds}秒`;

    return result || '即将过期';
  }

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Post('login')
  async login(@Body() loginData: { username: string; userType?: UserType }, @Res() res: Response) {
    // 模拟登录验证
    const user: User = {
      id: 1,
      username: loginData.username,
      userType: loginData.userType || UserType.NORMAL, // 默认为普通用户
    };

    // 创建session
    const sessionId = await this.sessionService.createSession(user);

    // 设置cookie
    res.cookie('sessionId', sessionId, { httpOnly: true });
    return res.json({
      success: true,
      message: 'Login successful',
      userType: user.userType,
      sessionTtl: this.getSessionTtlInfo(user.userType)
    });
  }

  @Get('profile')
  async getProfile(@Req() req: Request) {
    const sessionId = req.cookies['sessionId'];
    if (!sessionId) {
      return { success: false, message: 'Not authenticated' };
    }
    
    const userData = await this.sessionService.getSession(sessionId);
    if (!userData) {
      return { success: false, message: 'Invalid session' };
    }
    
    return { success: true, user: userData };
  }

  @Post('logout')
  async logout(@Req() req: Request, @Res() res: Response) {
    const sessionId = req.cookies['sessionId'];
    if (sessionId) {
      await this.sessionService.deleteSession(sessionId);
    }

    res.clearCookie('sessionId');
    return res.json({ success: true, message: 'Logged out successfully' });
  }

  @Get('session/info')
  async getSessionInfo(@Req() req: Request) {
    const sessionId = req.cookies['sessionId'];
    if (!sessionId) {
      return { success: false, message: 'Not authenticated' };
    }

    const sessionData = await this.sessionService.getSession(sessionId);
    if (!sessionData) {
      return { success: false, message: 'Invalid session' };
    }

    // 获取session的剩余TTL
    const remainingTtl = await this.sessionService.getSessionTtl(sessionId);
    const ttlInfo = this.getSessionTtlInfo(sessionData.userType);

    return {
      success: true,
      sessionId: sessionId,
      sessionData: sessionData,
      ttlInfo: {
        ...ttlInfo,
        remainingSeconds: remainingTtl,
        remainingTime: this.formatRemainingTime(remainingTtl)
      }
    };
  }

  @Post('upgrade-user')
  async upgradeUser(@Req() req: Request, @Body() body: { userType: UserType }) {
    const sessionId = req.cookies['sessionId'];
    if (!sessionId) {
      return { success: false, message: 'Not authenticated' };
    }

    const currentSession = await this.sessionService.getSession(sessionId);
    if (!currentSession) {
      return { success: false, message: 'Invalid session' };
    }

    // 更新用户类型
    const success = await this.sessionService.updateSession(sessionId, {
      userType: body.userType
    });

    if (success) {
      return {
        success: true,
        message: `User upgraded to ${body.userType}`,
        newTtl: this.getSessionTtlInfo(body.userType)
      };
    } else {
      return { success: false, message: 'Failed to upgrade user' };
    }
  }
}