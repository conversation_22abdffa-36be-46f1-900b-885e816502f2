{"name": "nest-redis-session-demo", "version": "1.0.0", "description": "NestJS Redis Session Demo", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "dev": "ts-node src/main.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "axios": "^1.9.0", "cookie-parser": "^1.4.6", "redis": "^4.6.7", "reflect-metadata": "^0.1.13", "uuid": "^9.0.0"}, "devDependencies": {"@types/cookie-parser": "^1.4.3", "@types/express": "^4.17.17", "@types/node": "^20.3.1", "@types/uuid": "^9.0.2", "ts-node": "^10.9.1", "typescript": "^5.1.3"}}