/**
 * Session查询示例
 * 演示登录后如何查询session信息
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function sessionQueryExample() {
  console.log('🔍 Session查询示例\n');

  try {
    // 1. 登录获取session
    console.log('1. 登录VIP用户...');
    const loginResponse = await axios.post(`${BASE_URL}/login`, {
      username: 'vip_test_user',
      userType: 'vip'
    });
    
    console.log('登录响应:', loginResponse.data);
    
    // 从响应头中获取cookie
    const cookies = loginResponse.headers['set-cookie'];
    let sessionId = '';
    if (cookies) {
      const sessionCookie = cookies.find(cookie => cookie.startsWith('sessionId='));
      if (sessionCookie) {
        sessionId = sessionCookie.split(';')[0].split('=')[1];
        console.log('获取到sessionId:', sessionId);
      }
    }
    console.log('');

    // 2. 使用 /profile 接口查询基本用户信息
    console.log('2. 查询基本用户信息 (/profile)...');
    const profileResponse = await axios.get(`${BASE_URL}/profile`, {
      headers: {
        'Cookie': `sessionId=${sessionId}`
      }
    });
    console.log('用户信息:', JSON.stringify(profileResponse.data, null, 2));
    console.log('');

    // 3. 使用 /session/info 接口查询详细session信息
    console.log('3. 查询详细session信息 (/session/info)...');
    const sessionInfoResponse = await axios.get(`${BASE_URL}/session/info`, {
      headers: {
        'Cookie': `sessionId=${sessionId}`
      }
    });
    console.log('详细session信息:', JSON.stringify(sessionInfoResponse.data, null, 2));
    console.log('');

    // 4. 等待几秒后再次查询，观察TTL变化
    console.log('4. 等待3秒后再次查询TTL变化...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const sessionInfoResponse2 = await axios.get(`${BASE_URL}/session/info`, {
      headers: {
        'Cookie': `sessionId=${sessionId}`
      }
    });
    console.log('3秒后的session信息:');
    console.log('剩余时间:', sessionInfoResponse2.data.ttlInfo.remainingTime);
    console.log('剩余秒数:', sessionInfoResponse2.data.ttlInfo.remainingSeconds);
    console.log('');

    // 5. 升级用户类型并查看TTL变化
    console.log('5. 升级用户为SVIP并查看TTL变化...');
    const upgradeResponse = await axios.post(`${BASE_URL}/upgrade-user`, {
      userType: 'svip'
    }, {
      headers: {
        'Cookie': `sessionId=${sessionId}`
      }
    });
    console.log('升级响应:', upgradeResponse.data);

    // 查询升级后的session信息
    const sessionInfoResponse3 = await axios.get(`${BASE_URL}/session/info`, {
      headers: {
        'Cookie': `sessionId=${sessionId}`
      }
    });
    console.log('升级后的session信息:');
    console.log('用户类型:', sessionInfoResponse3.data.sessionData.userType);
    console.log('新的TTL配置:', sessionInfoResponse3.data.ttlInfo.description);
    console.log('剩余时间:', sessionInfoResponse3.data.ttlInfo.remainingTime);
    console.log('');

    console.log('✅ Session查询示例完成！');

  } catch (error) {
    console.error('❌ 查询失败:', error.message);
    if (error.response) {
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行示例
if (require.main === module) {
  console.log('请确保服务器正在运行 (npm run dev)');
  console.log('然后运行: node session-query-example.js\n');
  
  setTimeout(sessionQueryExample, 2000);
}

module.exports = { sessionQueryExample };
