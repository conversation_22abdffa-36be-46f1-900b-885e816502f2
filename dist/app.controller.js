"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppController = void 0;
const common_1 = require("@nestjs/common");
const app_service_1 = require("./app.service");
const session_service_1 = require("./session/session.service");
const redis_config_1 = require("./config/redis.config");
let AppController = class AppController {
    constructor(appService, sessionService) {
        this.appService = appService;
        this.sessionService = sessionService;
    }
    getSessionTtlInfo(userType) {
        const ttl = redis_config_1.redisConfig.ttl[userType];
        const days = Math.floor(ttl / 86400);
        const hours = Math.floor((ttl % 86400) / 3600);
        let description = '';
        if (days > 0) {
            description = `${days}天`;
            if (hours > 0)
                description += `${hours}小时`;
        }
        else {
            description = `${hours}小时`;
        }
        return {
            seconds: ttl,
            description: description
        };
    }
    formatRemainingTime(seconds) {
        if (seconds <= 0)
            return '已过期';
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;
        let result = '';
        if (days > 0)
            result += `${days}天`;
        if (hours > 0)
            result += `${hours}小时`;
        if (minutes > 0)
            result += `${minutes}分钟`;
        if (remainingSeconds > 0 && days === 0 && hours === 0)
            result += `${remainingSeconds}秒`;
        return result || '即将过期';
    }
    getHello() {
        return this.appService.getHello();
    }
    async login(loginData, res) {
        const user = {
            id: 1,
            username: loginData.username,
            userType: loginData.userType || redis_config_1.UserType.NORMAL,
        };
        const sessionId = await this.sessionService.createSession(user);
        res.cookie('sessionId', sessionId, { httpOnly: true });
        return res.json({
            success: true,
            message: 'Login successful',
            userType: user.userType,
            sessionTtl: this.getSessionTtlInfo(user.userType)
        });
    }
    async getProfile(req) {
        const sessionId = req.cookies['sessionId'];
        if (!sessionId) {
            return { success: false, message: 'Not authenticated' };
        }
        const userData = await this.sessionService.getSession(sessionId);
        if (!userData) {
            return { success: false, message: 'Invalid session' };
        }
        return { success: true, user: userData };
    }
    async logout(req, res) {
        const sessionId = req.cookies['sessionId'];
        if (sessionId) {
            await this.sessionService.deleteSession(sessionId);
        }
        res.clearCookie('sessionId');
        return res.json({ success: true, message: 'Logged out successfully' });
    }
    async getSessionInfo(req) {
        const sessionId = req.cookies['sessionId'];
        if (!sessionId) {
            return { success: false, message: 'Not authenticated' };
        }
        const sessionData = await this.sessionService.getSession(sessionId);
        if (!sessionData) {
            return { success: false, message: 'Invalid session' };
        }
        const remainingTtl = await this.sessionService.getSessionTtl(sessionId);
        const ttlInfo = this.getSessionTtlInfo(sessionData.userType);
        return {
            success: true,
            sessionId: sessionId,
            sessionData: sessionData,
            ttlInfo: Object.assign(Object.assign({}, ttlInfo), { remainingSeconds: remainingTtl, remainingTime: this.formatRemainingTime(remainingTtl) })
        };
    }
    async upgradeUser(req, body) {
        const sessionId = req.cookies['sessionId'];
        if (!sessionId) {
            return { success: false, message: 'Not authenticated' };
        }
        const currentSession = await this.sessionService.getSession(sessionId);
        if (!currentSession) {
            return { success: false, message: 'Invalid session' };
        }
        const success = await this.sessionService.updateSession(sessionId, {
            userType: body.userType
        });
        if (success) {
            return {
                success: true,
                message: `User upgraded to ${body.userType}`,
                newTtl: this.getSessionTtlInfo(body.userType)
            };
        }
        else {
            return { success: false, message: 'Failed to upgrade user' };
        }
    }
};
exports.AppController = AppController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", String)
], AppController.prototype, "getHello", null);
__decorate([
    (0, common_1.Post)('login'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "login", null);
__decorate([
    (0, common_1.Get)('profile'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Post)('logout'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "logout", null);
__decorate([
    (0, common_1.Get)('session/info'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "getSessionInfo", null);
__decorate([
    (0, common_1.Post)('upgrade-user'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "upgradeUser", null);
exports.AppController = AppController = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [app_service_1.AppService,
        session_service_1.SessionService])
], AppController);
//# sourceMappingURL=app.controller.js.map