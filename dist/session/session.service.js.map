{"version": 3, "file": "session.service.js", "sourceRoot": "", "sources": ["../../src/session/session.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AAEpD,+BAAoC;AACpC,yDAA+D;AAIxD,IAAM,cAAc,GAApB,MAAM,cAAc;IACzB,YAC2C,WAA4B;QAA5B,gBAAW,GAAX,WAAW,CAAiB;IACpE,CAAC;IAKI,gBAAgB,CAAC,QAAkB;QACzC,OAAO,0BAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,0BAAW,CAAC,UAAU,CAAC;IAC7D,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,QAAc;QAChC,MAAM,SAAS,GAAG,IAAA,SAAM,GAAE,CAAC;QAC3B,MAAM,WAAW,mCACZ,QAAQ,KACX,gBAAgB,EAAE,IAAI,IAAI,EAAE,GAC7B,CAAC;QAGF,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAErD,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CACxB,WAAW,SAAS,EAAE,EACtB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAC3B;YACE,EAAE,EAAE,GAAG;SACR,CACF,CAAC;QACF,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAiB;QAChC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,SAAS,EAAE,CAAC,CAAC;QAChE,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAEvB,MAAM,WAAW,GAAgB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAGlD,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACxD,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,SAAS,EAAE,EAAE,GAAG,CAAC,CAAC;QAE3D,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,QAAuB;QAC5D,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe;YAAE,OAAO,KAAK,CAAC;QAGnC,MAAM,kBAAkB,iDACnB,eAAe,GACf,QAAQ,KAEX,gBAAgB,EAAE,eAAe,CAAC,gBAAgB,GACnD,CAAC;QAGF,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAE/D,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CACxB,WAAW,SAAS,EAAE,EACtB,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAClC;YACE,EAAE,EAAE,GAAG;SACR,CACF,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAiB;QACnC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,SAAS,EAAE,CAAC,CAAC;QAClE,OAAO,MAAM,KAAK,CAAC,CAAC;IACtB,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,SAAiB;QACnC,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,SAAS,EAAE,CAAC,CAAC;QAC/D,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;CACF,CAAA;AAlFY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,cAAc,CAAC,CAAA;;GAFd,cAAc,CAkF1B"}