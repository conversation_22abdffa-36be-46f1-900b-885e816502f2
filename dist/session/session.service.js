"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionService = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
const redis_config_1 = require("../config/redis.config");
let SessionService = class SessionService {
    constructor(redisClient) {
        this.redisClient = redisClient;
    }
    getTtlByUserType(userType) {
        return redis_config_1.redisConfig.ttl[userType] || redis_config_1.redisConfig.defaultTtl;
    }
    async createSession(userData) {
        const sessionId = (0, uuid_1.v4)();
        const sessionData = Object.assign(Object.assign({}, userData), { sessionCreatedAt: new Date() });
        const ttl = this.getTtlByUserType(userData.userType);
        await this.redisClient.set(`session:${sessionId}`, JSON.stringify(sessionData), {
            EX: ttl,
        });
        return sessionId;
    }
    async getSession(sessionId) {
        const data = await this.redisClient.get(`session:${sessionId}`);
        if (!data)
            return null;
        const sessionData = JSON.parse(data);
        const ttl = this.getTtlByUserType(sessionData.userType);
        await this.redisClient.expire(`session:${sessionId}`, ttl);
        return sessionData;
    }
    async updateSession(sessionId, userData) {
        const existingSession = await this.getSession(sessionId);
        if (!existingSession)
            return false;
        const updatedSessionData = Object.assign(Object.assign(Object.assign({}, existingSession), userData), { sessionCreatedAt: existingSession.sessionCreatedAt });
        const ttl = this.getTtlByUserType(updatedSessionData.userType);
        await this.redisClient.set(`session:${sessionId}`, JSON.stringify(updatedSessionData), {
            EX: ttl,
        });
        return true;
    }
    async deleteSession(sessionId) {
        const result = await this.redisClient.del(`session:${sessionId}`);
        return result === 1;
    }
    async getSessionTtl(sessionId) {
        const ttl = await this.redisClient.ttl(`session:${sessionId}`);
        return ttl > 0 ? ttl : 0;
    }
};
exports.SessionService = SessionService;
exports.SessionService = SessionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('REDIS_CLIENT')),
    __metadata("design:paramtypes", [Object])
], SessionService);
//# sourceMappingURL=session.service.js.map