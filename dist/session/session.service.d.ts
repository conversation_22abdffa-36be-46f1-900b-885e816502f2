import { RedisClientType } from 'redis';
import { User, SessionData } from '../interfaces/user.interface';
export declare class SessionService {
    private readonly redisClient;
    constructor(redisClient: RedisClientType);
    private getTtlByUserType;
    createSession(userData: User): Promise<string>;
    getSession(sessionId: string): Promise<SessionData | null>;
    updateSession(sessionId: string, userData: Partial<User>): Promise<boolean>;
    deleteSession(sessionId: string): Promise<boolean>;
    getSessionTtl(sessionId: string): Promise<number>;
}
