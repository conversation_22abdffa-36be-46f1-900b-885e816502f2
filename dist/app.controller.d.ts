import { AppService } from './app.service';
import { SessionService } from './session/session.service';
import { Request, Response } from 'express';
import { UserType } from './config/redis.config';
export declare class AppController {
    private readonly appService;
    private readonly sessionService;
    constructor(appService: AppService, sessionService: SessionService);
    private getSessionTtlInfo;
    private formatRemainingTime;
    getHello(): string;
    login(loginData: {
        username: string;
        userType?: UserType;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    getProfile(req: Request): Promise<{
        success: boolean;
        message: string;
        user?: undefined;
    } | {
        success: boolean;
        user: import("./interfaces/user.interface").SessionData;
        message?: undefined;
    }>;
    logout(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    getSessionInfo(req: Request): Promise<{
        success: boolean;
        message: string;
        sessionId?: undefined;
        sessionData?: undefined;
        ttlInfo?: undefined;
    } | {
        success: boolean;
        sessionId: any;
        sessionData: import("./interfaces/user.interface").SessionData;
        ttlInfo: {
            remainingSeconds: number;
            remainingTime: string;
            seconds: number;
            description: string;
        };
        message?: undefined;
    }>;
    upgradeUser(req: Request, body: {
        userType: UserType;
    }): Promise<{
        success: boolean;
        message: string;
        newTtl?: undefined;
    } | {
        success: boolean;
        message: string;
        newTtl: {
            seconds: number;
            description: string;
        };
    }>;
}
