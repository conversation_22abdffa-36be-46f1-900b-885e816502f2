# Redis Session TTL 功能说明

## 概述

本项目实现了基于用户类型的差异化 TTL (Time To Live) 功能，不同类型的用户享有不同的会话过期时间。

## 用户类型和TTL配置

| 用户类型 | TTL时间 | 秒数 | 说明 |
|---------|---------|------|------|
| normal  | 24小时  | 86400 | 普通用户 |
| vip     | 7天     | 604800 | VIP用户 |
| svip    | 30天    | 2592000 | 超级VIP用户 |

## 主要功能

### 1. 登录时指定用户类型

```bash
# 普通用户登录
curl -X POST http://localhost:3000/login \
  -H "Content-Type: application/json" \
  -d '{"username": "test_user", "userType": "normal"}'

# VIP用户登录
curl -X POST http://localhost:3000/login \
  -H "Content-Type: application/json" \
  -d '{"username": "vip_user", "userType": "vip"}'

# SVIP用户登录
curl -X POST http://localhost:3000/login \
  -H "Content-Type: application/json" \
  -d '{"username": "svip_user", "userType": "svip"}'
```

### 2. 用户类型升级

```bash
# 升级用户类型（需要先登录）
curl -X POST http://localhost:3000/upgrade-user \
  -H "Content-Type: application/json" \
  -H "Cookie: sessionId=your_session_id" \
  -d '{"userType": "vip"}'
```

### 3. 查询Session信息

#### 3.1 获取基本用户信息

```bash
# 获取当前用户基本信息
curl -X GET http://localhost:3000/profile \
  -H "Cookie: sessionId=your_session_id"
```

#### 3.2 获取详细Session信息

```bash
# 获取详细session信息，包括TTL剩余时间
curl -X GET http://localhost:3000/session/info \
  -H "Cookie: sessionId=your_session_id"
```

**响应示例：**
```json
{
  "success": true,
  "sessionId": "uuid-session-id",
  "sessionData": {
    "id": 1,
    "username": "vip_user",
    "userType": "vip",
    "sessionCreatedAt": "2024-01-01T10:00:00.000Z"
  },
  "ttlInfo": {
    "seconds": 604800,
    "description": "7天",
    "remainingSeconds": 603850,
    "remainingTime": "6天23小时57分钟"
  }
}
```

## 技术实现

### 1. 配置文件 (`src/config/redis.config.ts`)

```typescript
export enum UserType {
  NORMAL = 'normal',
  VIP = 'vip',
  SVIP = 'svip'
}

export const redisConfig = {
  host: 'localhost',
  port: 6379,
  ttl: {
    [UserType.NORMAL]: 86400,    // 24小时
    [UserType.VIP]: 604800,      // 7天
    [UserType.SVIP]: 2592000,    // 30天
  },
  defaultTtl: 86400,
};
```

### 2. 会话服务 (`src/session/session.service.ts`)

- `createSession()`: 根据用户类型设置相应的TTL
- `getSession()`: 获取会话时自动刷新TTL
- `updateSession()`: 更新会话数据时保持用户类型对应的TTL

### 3. 用户接口 (`src/interfaces/user.interface.ts`)

```typescript
export interface User {
  id: number;
  username: string;
  userType: UserType;
  email?: string;
  createdAt?: Date;
}

export interface SessionData extends User {
  sessionCreatedAt: Date;
}
```

## API接口列表

| 接口 | 方法 | 说明 | 需要认证 |
|------|------|------|----------|
| `/login` | POST | 用户登录，可指定userType | 否 |
| `/profile` | GET | 获取当前用户基本信息 | 是 |
| `/session/info` | GET | 获取详细session信息和TTL | 是 |
| `/upgrade-user` | POST | 升级用户类型 | 是 |
| `/logout` | POST | 退出登录 | 是 |

## 测试

### 运行测试脚本

```bash
# 启动服务器
npm run dev

# 在另一个终端运行基本功能测试
node test-ttl.js

# 运行session查询示例
node session-query-example.js
```

### 手动测试

1. 启动Redis服务器
2. 启动应用: `npm run dev`
3. 使用上述API进行测试

## 特性

1. **自动TTL管理**: 根据用户类型自动设置和刷新TTL
2. **类型安全**: 使用TypeScript枚举确保用户类型的类型安全
3. **向后兼容**: 支持默认TTL，不指定用户类型时使用普通用户TTL
4. **会话刷新**: 每次访问会话时自动刷新TTL
5. **用户升级**: 支持运行时升级用户类型并更新TTL

## 注意事项

1. 用户类型升级后，新的TTL会立即生效
2. 会话数据包含用户类型信息，用于TTL计算
3. 所有TTL相关操作都是基于用户类型动态计算的
4. Redis连接失败时会使用默认TTL配置
